<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Importer</title>
    <style>
        body { font-family: sans-serif; margin: 2em; }
        .container { max-width: 800px; margin: 0 auto; }
        .input-group { margin-bottom: 1em; }
        label { display: block; margin-bottom: .5em; }
        input[type="file"] { width: 100%; }
        button { padding: .5em 1em; cursor: pointer; }
        pre { background-color: #f4f4f4; padding: 1em; white-space: pre-wrap; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>File Importer</h1>

        <div class="input-group">
            <label for="separate-files">Import separate index.html, styles.css, and script.js files:</label>
            <input type="file" id="html-file" accept=".html" placeholder="index.html">
            <input type="file" id="css-file" accept=".css" placeholder="styles.css">
            <input type="file" id="js-file" accept=".js" placeholder="script.js">
        </div>

        <div class="input-group">
            <label for="combined-file">Import a single combined HTML file:</label>
            <input type="file" id="combined-file" accept=".html">
        </div>

        <div class="input-group">
            <label for="zip-file">Import from a ZIP file:</label>
            <input type="file" id="zip-file" accept=".zip">
            <button id="list-zip-contents-btn">List ZIP Contents</button>
        </div>
        <div class="input-group">
            <h3>ZIP File Contents:</h3>
            <pre id="zip-contents"></pre>
        </div>
        
        <div class="input-group">
            <label for="folder-input">Import from a folder:</label>
            <input type="file" id="folder-input" webkitdirectory directory>
        </div>

        <button id="process-btn">Process Files</button>

        <h2>Imported File Content:</h2>
        <h3>HTML Content:</h3>
        <pre id="html-content"></pre>
        <h3>CSS Content:</h3>
        <pre id="css-content"></pre>
        <h3>JS Content:</h3>
        <pre id="js-content"></pre>
    </div>

    <script>
        let htmlContent = '';
        let cssContent = '';
        let jsContent = '';

        const htmlFileInput = document.getElementById('html-file');
        const cssFileInput = document.getElementById('css-file');
        const jsFileInput = document.getElementById('js-file');
        const combinedFileInput = document.getElementById('combined-file');
        const zipFileInput = document.getElementById('zip-file');
        const folderInput = document.getElementById('folder-input');
        const processBtn = document.getElementById('process-btn');
        const listZipContentsBtn = document.getElementById('list-zip-contents-btn');
        const htmlContentEl = document.getElementById('html-content');
        const cssContentEl = document.getElementById('css-content');
        const jsContentEl = document.getElementById('js-content');
        const zipContentsEl = document.getElementById('zip-contents');

        processBtn.addEventListener('click', async () => {
            // Reset content
            htmlContent = cssContent = jsContent = '';
            htmlContentEl.textContent = cssContentEl.textContent = jsContentEl.textContent = '';

            // 1. Separate files
            if (htmlFileInput.files.length > 0 && cssFileInput.files.length > 0 && jsFileInput.files.length > 0) {
                htmlContent = await htmlFileInput.files[0].text();
                cssContent = await cssFileInput.files[0].text();
                jsContent = await jsFileInput.files[0].text();
            } 
            // 2. Combined file
            else if (combinedFileInput.files.length > 0) {
                const combinedHtml = await combinedFileInput.files[0].text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(combinedHtml, 'text/html');
                
                const styleTag = doc.querySelector('style');
                if (styleTag) {
                    cssContent = styleTag.innerHTML;
                    styleTag.remove();
                }

                const scriptTag = doc.querySelector('script');
                if (scriptTag) {
                    jsContent = scriptTag.innerHTML;
                    scriptTag.remove();
                }
                
                htmlContent = doc.body.innerHTML;

            } 
            // 3. Folder input
            else if (folderInput.files.length > 0) {
                for (const file of folderInput.files) {
                    if (file.name.endsWith('index.html')) {
                        htmlContent = await file.text();
                    } else if (file.name.endsWith('styles.css')) {
                        cssContent = await file.text();
                    } else if (file.name.endsWith('script.js')) {
                        jsContent = await file.text();
                    }
                }
            }
            
            // 4. ZIP file
            else if (zipFileInput.files.length > 0) {
                const zipFile = zipFileInput.files[0];
                const zip = await JSZip.loadAsync(zipFile);
                const htmlFile = zip.file("index.html");
                const cssFile = zip.file("styles.css");
                const jsFile = zip.file("script.js");

                if (htmlFile) {
                    htmlContent = await htmlFile.async("string");
                }
                if (cssFile) {
                    cssContent = await cssFile.async("string");
                }
                if (jsFile) {
                    jsContent = await jsFile.async("string");
                }

                if (!htmlFile && !cssFile && !jsFile) {
                    alert("The selected ZIP file does not contain index.html, styles.css, or script.js.");
                }
            }


            // Display content
            htmlContentEl.textContent = htmlContent;
            cssContentEl.textContent = cssContent;
            jsContentEl.textContent = jsContent;
        });

        listZipContentsBtn.addEventListener('click', async () => {
            zipContentsEl.textContent = '';
            if (zipFileInput.files.length > 0) {
                const zipFile = zipFileInput.files[0];
                const zip = await JSZip.loadAsync(zipFile);
                let contents = '';
                zip.forEach((relativePath, zipEntry) => {
                    contents += `${zipEntry.name}\n`;
                });
                zipContentsEl.textContent = contents;
            } else {
                alert('Please select a ZIP file first.');
            }
        });
    </script>
</body>
</html>
