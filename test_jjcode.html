<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JJCodeGemini Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4F46E5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3730A3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #D1FAE5; color: #065F46; }
        .error { background: #FEE2E2; color: #991B1B; }
        .info { background: #DBEAFE; color: #1E40AF; }
    </style>
</head>
<body>
    <h1>JJCodeGemini Diagnostic Test</h1>
    
    <div class="test-section">
        <h2>1. Basic Functionality Test</h2>
        <button class="test-button" onclick="testBasicFunctionality()">Test Basic Functions</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. API Key Test</h2>
        <input type="text" id="api-key-test" placeholder="Enter your Gemini API key" style="width: 300px; padding: 8px; margin-right: 10px;">
        <button class="test-button" onclick="testApiKey()">Test API Key</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Code Generation Test</h2>
        <input type="text" id="prompt-test" placeholder="Enter a simple prompt (e.g., 'red button')" style="width: 300px; padding: 8px; margin-right: 10px;">
        <button class="test-button" onclick="testCodeGeneration()">Test Generation</button>
        <div id="generation-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Local Storage Test</h2>
        <button class="test-button" onclick="testLocalStorage()">Test Storage</button>
        <div id="storage-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. Open JJCodeGemini</h2>
        <button class="test-button" onclick="openJJCode()">Open JJCodeGemini</button>
        <div id="open-result" class="result"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function testBasicFunctionality() {
            try {
                // Test if basic web APIs are available
                const tests = [
                    { name: 'localStorage', test: () => typeof localStorage !== 'undefined' },
                    { name: 'fetch API', test: () => typeof fetch !== 'undefined' },
                    { name: 'Promise', test: () => typeof Promise !== 'undefined' },
                    { name: 'JSON', test: () => typeof JSON !== 'undefined' }
                ];

                const results = tests.map(t => `${t.name}: ${t.test() ? '✓' : '✗'}`);
                showResult('basic-result', results.join(', '), 'success');
            } catch (error) {
                showResult('basic-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testApiKey() {
            const apiKey = document.getElementById('api-key-test').value.trim();
            if (!apiKey) {
                showResult('api-result', 'Please enter an API key', 'error');
                return;
            }

            try {
                showResult('api-result', 'Testing API key...', 'info');
                
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ role: "user", parts: [{ text: "Hello" }] }]
                    })
                });

                if (response.ok) {
                    showResult('api-result', 'API key is valid! ✓', 'success');
                } else {
                    const error = await response.json();
                    showResult('api-result', `API Error: ${error.error?.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `Network Error: ${error.message}`, 'error');
            }
        }

        async function testCodeGeneration() {
            const prompt = document.getElementById('prompt-test').value.trim();
            const apiKey = document.getElementById('api-key-test').value.trim();
            
            if (!prompt) {
                showResult('generation-result', 'Please enter a prompt', 'error');
                return;
            }
            
            if (!apiKey) {
                showResult('generation-result', 'Please enter API key first', 'error');
                return;
            }

            try {
                showResult('generation-result', 'Generating code...', 'info');
                
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{
                            role: "user",
                            parts: [{ text: `Generate a simple HTML button that is ${prompt}. Return only HTML code.` }]
                        }]
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text || 'No content generated';
                    showResult('generation-result', `Code generated successfully! Length: ${generatedText.length} chars`, 'success');
                } else {
                    const error = await response.json();
                    showResult('generation-result', `Generation failed: ${error.error?.message}`, 'error');
                }
            } catch (error) {
                showResult('generation-result', `Error: ${error.message}`, 'error');
            }
        }

        function testLocalStorage() {
            try {
                // Test localStorage functionality
                const testKey = 'jjcode_test';
                const testValue = { test: true, timestamp: Date.now() };
                
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                localStorage.removeItem(testKey);
                
                if (retrieved && retrieved.test === true) {
                    showResult('storage-result', 'Local storage working correctly ✓', 'success');
                } else {
                    showResult('storage-result', 'Local storage test failed', 'error');
                }
            } catch (error) {
                showResult('storage-result', `Storage error: ${error.message}`, 'error');
            }
        }

        function openJJCode() {
            try {
                window.open('JJCODEGemini.html', '_blank');
                showResult('open-result', 'JJCodeGemini opened in new tab', 'success');
            } catch (error) {
                showResult('open-result', `Error opening: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on load
        window.onload = () => {
            testBasicFunctionality();
        };
    </script>
</body>
</html>
