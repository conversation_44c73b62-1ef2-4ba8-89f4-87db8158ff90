<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JJ Code AI Builder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --container-bg: #FFFFFF;
            --text-primary: #1F2937;
            --accent-color: #4F46E5;
            --border-color: #E5E7EB;
        }

        .dark {
            --primary-gradient: linear-gradient(135deg, #1F2937 0%, #111827 100%);
            --container-bg: #1F2937;
            --text-primary: #F9FAFB;
            --accent-color: #6366F1;
            --border-color: #374151;
        }

        body {
            background: var(--primary-gradient);
            color: var(--text-primary);
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .container-bg {
            background: var(--container-bg);
            transition: all 0.3s ease;
        }

        .accent-bg {
            background: var(--accent-color);
        }

        .accent-text {
            color: var(--accent-color);
        }

        .border-custom {
            border-color: var(--border-color);
        }

        .typewriter-cursor::after {
            content: '|';
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .code-editor {
            font-family: 'Courier New', monospace;
            background: var(--container-bg);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .api-key-input {
            background: var(--container-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .settings-panel {
            background: var(--container-bg);
            border: 1px solid var(--border-color);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .dark .settings-panel {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .gradient-text {
            background: linear-gradient(45deg, var(--accent-color), #8B5CF6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .slide-down {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-10px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .pulse-border {
            animation: pulseBorder 2s infinite;
        }

        @keyframes pulseBorder {
            0% { border-color: var(--accent-color); }
            50% { border-color: transparent; }
            100% { border-color: var(--accent-color); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="container-bg shadow-lg border-b border-custom">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 accent-bg rounded-lg flex items-center justify-center">
                        <i class="fas fa-code text-white text-lg"></i>
                    </div>
                    <h1 class="text-2xl font-bold gradient-text">JJ Code AI Builder</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="settingsBtn" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-cog text-xl"></i>
                    </button>
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-sun text-xl dark:hidden"></i>
                        <i class="fas fa-moon text-xl hidden dark:inline"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- API Settings Panel -->
    <div id="settingsPanel" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="settings-panel rounded-xl p-6 w-full max-w-md mx-4 slide-down">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold flex items-center">
                    <i class="fas fa-key accent-text mr-2"></i>
                    API Key Settings
                </h3>
                <button id="closeSettings" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">
                        <i class="fab fa-google mr-2 text-blue-500"></i>
                        Google Gemini API Key
                    </label>
                    <div class="relative">
                        <input type="password" id="apiKeyInput" placeholder="Enter your API key..." 
                               class="api-key-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                        <button id="toggleApiKey" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                        <div class="text-sm">
                            <p class="font-medium text-blue-800 dark:text-blue-200">How to get your API key:</p>
                            <ol class="mt-2 space-y-1 text-blue-700 dark:text-blue-300">
                                <li>1. Visit Google AI Studio</li>
                                <li>2. Sign in with your Google account</li>
                                <li>3. Generate a new API key</li>
                                <li>4. Copy and paste it here</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button id="saveApiKey" class="flex-1 accent-bg text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity font-medium">
                        <i class="fas fa-save mr-2"></i>
                        Save Key
                    </button>
                    <button id="testApiKey" class="flex-1 bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 transition-colors font-medium">
                        <i class="fas fa-check mr-2"></i>
                        Test
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">
                Turn Your Ideas into Code, Instantly
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Describe your web component in plain language, and let our advanced AI write the HTML, CSS, and JavaScript for you. The future of web development is here.
            </p>
        </div>

        <!-- Input Section -->
        <div class="container-bg rounded-2xl shadow-xl p-8 mb-8 border border-custom">
            <div class="space-y-6">
                <div>
                    <label for="promptInput" class="block text-lg font-medium mb-3">
                        <i class="fas fa-magic accent-text mr-2"></i>
                        Describe your component
                    </label>
                    <textarea id="promptInput" rows="4" 
                              placeholder="Example: Create a responsive pricing card with three tiers, hover effects, and a gradient background..."
                              class="w-full px-4 py-3 border border-custom rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all container-bg"></textarea>
                </div>
                
                <div class="flex justify-center">
                    <button id="generateBtn" class="accent-bg text-white px-8 py-4 rounded-xl hover:opacity-90 transition-all font-medium text-lg shadow-lg transform hover:scale-105">
                        <i class="fas fa-wand-magic-sparkles mr-2"></i>
                        Generate Code
                    </button>
                </div>
            </div>
        </div>

        <!-- Code and Preview Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Code Editor -->
            <div class="container-bg rounded-2xl shadow-xl border border-custom overflow-hidden">
                <div class="border-b border-custom px-6 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fas fa-code accent-text mr-2"></i>
                        Code Editor
                    </h3>
                    <div class="flex space-x-2">
                        <button id="copyBtn" class="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors text-sm font-medium">
                            <i class="fas fa-copy mr-1"></i>
                            Copy
                        </button>
                        <button id="clearBtn" class="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors text-sm font-medium">
                            <i class="fas fa-trash mr-1"></i>
                            Clear
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <pre id="codeEditor" class="code-editor p-6 h-96 overflow-auto text-sm typewriter-cursor"><code id="codeContent">// Your generated code will appear here...</code></pre>
                    <div id="loadingIndicator" class="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-90 flex items-center justify-center hidden">
                        <div class="text-center">
                            <div class="loading-spinner mx-auto mb-4 accent-text"></div>
                            <p class="text-sm font-medium">Generating your code...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Preview -->
            <div class="container-bg rounded-2xl shadow-xl border border-custom overflow-hidden">
                <div class="border-b border-custom px-6 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fas fa-eye accent-text mr-2"></i>
                        Live Preview
                    </h3>
                    <div class="flex space-x-2">
                        <button id="refreshPreview" class="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors text-sm font-medium">
                            <i class="fas fa-refresh mr-1"></i>
                            Refresh
                        </button>
                        <button id="fullscreenBtn" class="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors text-sm font-medium">
                            <i class="fas fa-expand mr-1"></i>
                            Fullscreen
                        </button>
                    </div>
                </div>
                <div class="relative bg-white">
                    <iframe id="previewFrame" class="w-full h-96 border-0" sandbox="allow-scripts allow-same-origin"></iframe>
                    <div id="previewPlaceholder" class="absolute inset-0 flex items-center justify-center text-gray-500">
                        <div class="text-center">
                            <i class="fas fa-code text-6xl mb-4 opacity-20"></i>
                            <p class="text-lg">Preview will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="mt-16">
            <h3 class="text-2xl font-bold text-center mb-8">Why Choose JJ Code AI Builder?</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="container-bg rounded-xl p-6 shadow-lg border border-custom text-center">
                    <div class="w-16 h-16 accent-bg rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-bolt text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-2">Lightning Fast</h4>
                    <p class="text-gray-600 dark:text-gray-400">Generate complete components in seconds with our advanced AI technology.</p>
                </div>
                
                <div class="container-bg rounded-xl p-6 shadow-lg border border-custom text-center">
                    <div class="w-16 h-16 accent-bg rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-2">Responsive Design</h4>
                    <p class="text-gray-600 dark:text-gray-400">All generated components are mobile-first and fully responsive.</p>
                </div>
                
                <div class="container-bg rounded-xl p-6 shadow-lg border border-custom text-center">
                    <div class="w-16 h-16 accent-bg rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-palette text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-2">Beautiful UI</h4>
                    <p class="text-gray-600 dark:text-gray-400">Modern, clean designs that follow current web standards and trends.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Fullscreen Modal -->
    <div id="fullscreenModal" class="fixed inset-0 bg-black bg-opacity-95 z-50 hidden">
        <div class="absolute top-4 right-4 flex space-x-2">
            <button id="closeFullscreen" class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition-all">
                <i class="fas fa-times mr-2"></i>
                Close
            </button>
        </div>
        <iframe id="fullscreenFrame" class="w-full h-full border-0" sandbox="allow-scripts allow-same-origin"></iframe>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-40 space-y-2"></div>

    <script>
        class JJCodeAIBuilder {
            constructor() {
                this.apiKey = localStorage.getItem('gemini_api_key') || '';
                this.theme = localStorage.getItem('theme') || 'light';
                this.isGenerating = false;
                this.typewriterInterval = null;
                
                this.initializeTheme();
                this.bindEvents();
                this.loadSavedData();
            }

            initializeTheme() {
                document.documentElement.className = this.theme;
            }

            bindEvents() {
                // Theme toggle
                document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
                
                // Settings panel
                document.getElementById('settingsBtn').addEventListener('click', () => this.openSettings());
                document.getElementById('closeSettings').addEventListener('click', () => this.closeSettings());
                
                // API key management
                document.getElementById('toggleApiKey').addEventListener('click', () => this.toggleApiKeyVisibility());
                document.getElementById('saveApiKey').addEventListener('click', () => this.saveApiKey());
                document.getElementById('testApiKey').addEventListener('click', () => this.testApiKey());
                
                // Code generation
                document.getElementById('generateBtn').addEventListener('click', () => this.generateCode());
                
                // Code editor actions
                document.getElementById('copyBtn').addEventListener('click', () => this.copyCode());
                document.getElementById('clearBtn').addEventListener('click', () => this.clearCode());
                document.getElementById('refreshPreview').addEventListener('click', () => this.refreshPreview());
                
                // Fullscreen
                document.getElementById('fullscreenBtn').addEventListener('click', () => this.openFullscreen());
                document.getElementById('closeFullscreen').addEventListener('click', () => this.closeFullscreen());
                
                // Close modals on outside click
                document.getElementById('settingsPanel').addEventListener('click', (e) => {
                    if (e.target.id === 'settingsPanel') this.closeSettings();
                });
                
                document.getElementById('fullscreenModal').addEventListener('click', (e) => {
                    if (e.target.id === 'fullscreenModal') this.closeFullscreen();
                });
                
                // Enter key in prompt input
                document.getElementById('promptInput').addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && e.ctrlKey) {
                        this.generateCode();
                    }
                });
            }

            loadSavedData() {
                if (this.apiKey) {
                    document.getElementById('apiKeyInput').value = this.apiKey;
                }
            }

            toggleTheme() {
                this.theme = this.theme === 'light' ? 'dark' : 'light';
                document.documentElement.className = this.theme;
                localStorage.setItem('theme', this.theme);
                this.showToast('Theme switched to ' + this.theme + ' mode', 'success');
            }

            openSettings() {
                document.getElementById('settingsPanel').classList.remove('hidden');
            }

            closeSettings() {
                document.getElementById('settingsPanel').classList.add('hidden');
            }

            toggleApiKeyVisibility() {
                const input = document.getElementById('apiKeyInput');
                const icon = document.getElementById('toggleApiKey').querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    input.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            }

            saveApiKey() {
                const apiKey = document.getElementById('apiKeyInput').value.trim();
                if (!apiKey) {
                    this.showToast('Please enter an API key', 'error');
                    return;
                }
                
                this.apiKey = apiKey;
                localStorage.setItem('gemini_api_key', apiKey);
                this.showToast('API key saved successfully', 'success');
                this.closeSettings();
            }

            async testApiKey() {
                const apiKey = document.getElementById('apiKeyInput').value.trim();
                if (!apiKey) {
                    this.showToast('Please enter an API key first', 'error');
                    return;
                }

                const testBtn = document.getElementById('testApiKey');
                const originalText = testBtn.innerHTML;
                testBtn.innerHTML = '<div class="loading-spinner inline-block mr-2"></div>Testing...';
                testBtn.disabled = true;

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: "Say 'API test successful' if you can read this."
                                }]
                            }]
                        })
                    });

                    if (response.ok) {
                        this.showToast('API key is valid!', 'success');
                        this.apiKey = apiKey;
                        localStorage.setItem('gemini_api_key', apiKey);
                    } else {
                        this.showToast('Invalid API key or network error', 'error');
                    }
                } catch (error) {
                    this.showToast('Failed to test API key: ' + error.message, 'error');
                } finally {
                    testBtn.innerHTML = originalText;
                    testBtn.disabled = false;
                }
            }

            async generateCode() {
                if (this.isGenerating) return;
                
                const prompt = document.getElementById('promptInput').value.trim();
                if (!prompt) {
                    this.showToast('Please enter a description for your component', 'error');
                    return;
                }

                if (!this.apiKey) {
                    this.showToast('Please configure your API key first', 'error');
                    this.openSettings();
                    return;
                }

                this.isGenerating = true;
                this.showLoading(true);
                
                const generateBtn = document.getElementById('generateBtn');
                const originalText = generateBtn.innerHTML;
                generateBtn.innerHTML = '<div class="loading-spinner inline-block mr-2"></div>Generating...';
                generateBtn.disabled = true;

                try {
                    const aiPrompt = `Create a single, complete, and self-contained HTML file for: ${prompt}

Requirements:
1. Include ALL CSS and JavaScript inline (no external files)
2. Use modern HTML5, CSS3, and vanilla JavaScript
3. Make it responsive and mobile-friendly
4. Use attractive colors and modern design
5. Include hover effects and smooth transitions
6. Add appropriate animations where suitable
7. Ensure cross-browser compatibility
8. Use semantic HTML elements
9. Make it accessible with proper ARIA labels
10. Return ONLY the HTML code in a markdown code block

The HTML should be complete and ready to save as an .html file.`;

                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${this.apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: aiPrompt
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.7,
                                topK: 40,
                                topP: 0.95,
                                maxOutputTokens: 8192,
                            }
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`API request failed: ${response.status}`);
                    }

                    const data = await response.json();
                    const generatedText = data.candidates[0].content.parts[0].text;
                    
                    // Extract HTML code from markdown
                    const codeMatch = generatedText.match(/```html\n([\s\S]*?)\n```/) || 
                                     generatedText.match(/```\n([\s\S]*?)\n```/) ||
                                     [null, generatedText];
                    
                    const htmlCode = codeMatch[1] || generatedText;
                    
                    this.showLoading(false);
                    this.typewriterEffect(htmlCode);
                    this.showToast('Code generated successfully!', 'success');

                } catch (error) {
                    this.showLoading(false);
                    this.showToast('Failed to generate code: ' + error.message, 'error');
                    console.error('Generation error:', error);
                } finally {
                    this.isGenerating = false;
                    generateBtn.innerHTML = originalText;
                    generateBtn.disabled = false;
                }
            }

            typewriterEffect(code) {
                const codeContent = document.getElementById('codeContent');
                const codeEditor = document.getElementById('codeEditor');
                
                codeContent.textContent = '';
                codeEditor.classList.add('typewriter-cursor');
                
                let index = 0;
                
                this.typewriterInterval = setInterval(() => {
                    if (index < code.length) {
                        codeContent.textContent += code[index];
                        index++;
                        
                        // Auto-scroll to bottom
                        codeEditor.scrollTop = codeEditor.scrollHeight;
                    } else {
                        clearInterval(this.typewriterInterval);
                        codeEditor.classList.remove('typewriter-cursor');
                        this.updatePreview(code);
                    }
                }, 10); // Adjust speed here (lower = faster)
            }

            updatePreview(code) {
                const previewFrame = document.getElementById('previewFrame');
                const placeholder = document.getElementById('previewPlaceholder');
                
                try {
                    const blob = new Blob([code], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    previewFrame.src = url;
                    placeholder.style.display = 'none';
                    
                    // Clean up old blob URLs
                    previewFrame.onload = () => {
                        setTimeout(() => URL.revokeObjectURL(url), 1000);
                    };
                } catch (error) {
                    this.showToast('Failed to update preview: ' + error.message, 'error');
                }
            }

            copyCode() {
                const codeContent = document.getElementById('codeContent').textContent;
                if (!codeContent || codeContent.includes('Your generated code will appear here')) {
                    this.showToast('No code to copy', 'error');
                    return;
                }

                navigator.clipboard.writeText(codeContent).then(() => {
                    this.showToast('Code copied to clipboard!', 'success');
                }).catch(() => {
                    this.showToast('Failed to copy code', 'error');
                });
            }

            clearCode() {
                if (confirm('Are you sure you want to clear the code editor?')) {
                    document.getElementById('codeContent').textContent = '// Your generated code will appear here...';
                    document.getElementById('previewFrame').src = '';
                    document.getElementById('previewPlaceholder').style.display = 'flex';
                    
                    if (this.typewriterInterval) {
                        clearInterval(this.typewriterInterval);
                        document.getElementById('codeEditor').classList.remove('typewriter-cursor');
                    }
                    
                    this.showToast('Code editor cleared', 'info');
                }
            }

            refreshPreview() {
                const codeContent = document.getElementById('codeContent').textContent;
                if (codeContent && !codeContent.includes('Your generated code will appear here')) {
                    this.updatePreview(codeContent);
                    this.showToast('Preview refreshed', 'success');
                }
            }

            openFullscreen() {
                const previewFrame = document.getElementById('previewFrame');
                const fullscreenFrame = document.getElementById('fullscreenFrame');
                const fullscreenModal = document.getElementById('fullscreenModal');
                
                fullscreenFrame.src = previewFrame.src;
                fullscreenModal.classList.remove('hidden');
            }

            closeFullscreen() {
                document.getElementById('fullscreenModal').classList.add('hidden');
                document.getElementById('fullscreenFrame').src = '';
            }

            showLoading(show) {
                const loadingIndicator = document.getElementById('loadingIndicator');
                loadingIndicator.classList.toggle('hidden', !show);
            }

            showToast(message, type = 'info') {
                const toastContainer = document.getElementById('toastContainer');
                const toast = document.createElement('div');
                
                const bgColor = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-blue-500',
                    warning: 'bg-yellow-500'
                }[type];

                const icon = {
                    success: 'fas fa-check-circle',
                    error: 'fas fa-exclamation-circle',
                    info: 'fas fa-info-circle',
                    warning: 'fas fa-exclamation-triangle'
                }[type];

                toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 transform transition-all duration-300 translate-x-full`;
                toast.innerHTML = `
                    <i class="${icon}"></i>
                    <span>${message}</span>
                    <button class="ml-2 hover:bg-white hover:bg-opacity-20 rounded p-1" onclick="this.parentElement.remove()">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                `;
                
                toastContainer.appendChild(toast);
                
                // Trigger animation
                setTimeout(() => toast.classList.remove('translate-x-full'), 100);
                
                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.classList.add('translate-x-full');
                        setTimeout(() => toast.remove(), 300);
                    }
                }, 5000);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new JJCodeAIBuilder();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'k':
                        e.preventDefault();
                        document.getElementById('settingsBtn').click();
                        break;
                    case 'Enter':
                        if (document.activeElement.id === 'promptInput') {
                            e.preventDefault();
                            document.getElementById('generateBtn').click();
                        }
                        break;
                    case 'c':
                        if (e.shiftKey) {
                            e.preventDefault();
                            document.getElementById('copyBtn').click();
                        }
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                document.getElementById('closeSettings').click();
                document.getElementById('closeFullscreen').click();
            }
        });
    </script>
</body>
</html>
