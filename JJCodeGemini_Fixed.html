<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JJ Code AI Builder - Fixed</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 600px;
        }

        .input-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .api-key-section {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .prompt-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #4F46E5;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }

        .button {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .output-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .code-editor {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            min-height: 200px;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .preview-section {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            min-height: 300px;
            background: white;
        }

        .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 6px;
            min-height: 300px;
        }

        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.info { background: #3b82f6; }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .status.success { background: #dcfce7; color: #166534; }
        .status.error { background: #fef2f2; color: #dc2626; }
        .status.info { background: #dbeafe; color: #1d4ed8; }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 JJ Code AI Builder - Fixed Version</h1>
            <p>Generate HTML code with AI - Simplified & Working</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <div class="api-key-section">
                    <label for="apiKey"><strong>Gemini API Key:</strong></label>
                    <input type="password" id="apiKey" placeholder="Enter your Gemini API key">
                    <button class="button" onclick="testApiKey()" style="margin-top: 10px; width: 100%;">Test API Key</button>
                    <div id="apiStatus"></div>
                </div>

                <div class="prompt-section">
                    <label for="prompt"><strong>Describe what you want to create:</strong></label>
                    <textarea id="prompt" placeholder="Example: Create a red button that says 'Click me' with hover effects"></textarea>
                    
                    <div class="controls">
                        <button class="button" onclick="generateCode()" id="generateBtn">Generate Code</button>
                        <button class="button" onclick="clearAll()" style="background: #6b7280;">Clear</button>
                        <button class="button" onclick="copyCode()" id="copyBtn" disabled>Copy Code</button>
                    </div>
                </div>
            </div>

            <div class="output-section">
                <div>
                    <strong>Generated Code:</strong>
                    <div class="code-editor" id="codeEditor">// Your generated code will appear here...</div>
                </div>
                
                <div>
                    <strong>Live Preview:</strong>
                    <div class="preview-section">
                        <iframe id="preview" class="preview-iframe"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentCode = '';

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Update status
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Test API Key
        async function testApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (!apiKey) {
                updateStatus('apiStatus', 'Please enter an API key', 'error');
                return;
            }

            updateStatus('apiStatus', 'Testing API key...', 'info');

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ role: "user", parts: [{ text: "Hello" }] }]
                    })
                });

                if (response.ok) {
                    updateStatus('apiStatus', '✅ API key is valid and working!', 'success');
                    localStorage.setItem('gemini_api_key', apiKey);
                    showNotification('API key saved successfully!', 'success');
                } else {
                    const error = await response.json();
                    updateStatus('apiStatus', `❌ API key test failed: ${error.error?.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                updateStatus('apiStatus', `❌ Network error: ${error.message}`, 'error');
            }
        }

        // Generate Code
        async function generateCode() {
            const prompt = document.getElementById('prompt').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();

            if (!prompt) {
                showNotification('Please enter a description', 'error');
                return;
            }

            if (!apiKey) {
                showNotification('Please enter your API key first', 'error');
                return;
            }

            const generateBtn = document.getElementById('generateBtn');
            const copyBtn = document.getElementById('copyBtn');
            
            generateBtn.disabled = true;
            generateBtn.textContent = 'Generating...';
            copyBtn.disabled = true;

            document.getElementById('codeEditor').textContent = '// Generating code, please wait...';

            try {
                const fullPrompt = `Generate a complete, self-contained HTML file for: "${prompt}". 
                Include all CSS and JavaScript inline. Make it functional and visually appealing. 
                Return ONLY the HTML code, no explanations.`;

                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ role: "user", parts: [{ text: fullPrompt }] }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 8192
                        }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    let generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

                    // Extract HTML from markdown if present
                    const htmlMatch = generatedText.match(/```html\s*([\s\S]*?)\s*```/);
                    if (htmlMatch) {
                        generatedText = htmlMatch[1].trim();
                    } else {
                        // Remove any markdown formatting
                        generatedText = generatedText.replace(/```[\s\S]*?```/g, '').trim();
                    }

                    if (generatedText) {
                        currentCode = generatedText;
                        document.getElementById('codeEditor').textContent = currentCode;
                        updatePreview(currentCode);
                        copyBtn.disabled = false;
                        showNotification('Code generated successfully!', 'success');
                    } else {
                        throw new Error('No code was generated');
                    }
                } else {
                    const error = await response.json();
                    throw new Error(error.error?.message || 'API request failed');
                }
            } catch (error) {
                document.getElementById('codeEditor').textContent = `// Error: ${error.message}`;
                showNotification(`Generation failed: ${error.message}`, 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = 'Generate Code';
            }
        }

        // Update Preview
        function updatePreview(code) {
            const iframe = document.getElementById('preview');
            iframe.srcdoc = code;
        }

        // Copy Code
        async function copyCode() {
            if (!currentCode) {
                showNotification('No code to copy', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(currentCode);
                showNotification('Code copied to clipboard!', 'success');
            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = currentCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('Code copied to clipboard!', 'success');
            }
        }

        // Clear All
        function clearAll() {
            document.getElementById('prompt').value = '';
            document.getElementById('codeEditor').textContent = '// Your generated code will appear here...';
            document.getElementById('preview').srcdoc = '';
            document.getElementById('copyBtn').disabled = true;
            currentCode = '';
            showNotification('Cleared successfully!', 'info');
        }

        // Load saved API key on page load
        window.addEventListener('load', () => {
            const savedKey = localStorage.getItem('gemini_api_key');
            if (savedKey) {
                document.getElementById('apiKey').value = savedKey;
                updateStatus('apiStatus', '✅ API key loaded from storage', 'success');
            }
        });
    </script>
</body>
</html>
