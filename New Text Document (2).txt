<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JJ Code AI Builder</title>
    <style>
        /* CSS Reset and Global Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            min-height: 100vh;
            transition: all 0.3s ease;
            color: var(--text-color);
            background-image: var(--background-gradient);
            background-attachment: fixed;
        }

        /* Color Palette Variables */
        :root {
            --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --container-bg: #FFFFFF;
            --text-color: #1F2937;
            --accent-color: #4F46E5;
            --border-color: #e5e7eb;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        body.dark {
            --background-gradient: linear-gradient(135deg, #1F2937 0%, #111827 100%);
            --container-bg: #1F2937;
            --text-color: #F9FAFB;
            --accent-color: #6366F1;
            --border-color: #374151;
            --shadow-color: rgba(255, 255, 255, 0.1);
        }

        /* Layout and Component Styling */
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background-color: var(--container-bg);
            box-shadow: 0 4px 6px -1px var(--shadow-color);
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .theme-switcher {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .theme-switcher:hover {
            background-color: var(--border-color);
        }

        .theme-switcher svg {
            width: 24px;
            height: 24px;
            fill: var(--text-color);
            transition: all 0.3s ease;
        }

        .hero-section {
            text-align: center;
            padding: 2rem;
            border-radius: 0.75rem;
            background-color: var(--container-bg);
            box-shadow: 0 10px 15px -3px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .hero-section h2 {
            font-size: 2.25rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .hero-section p {
            font-size: 1.125rem;
            color: var(--text-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .prompt-input-group {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .prompt-textarea {
            width: 100%;
            height: 150px;
            padding: 1rem;
            border-radius: 0.75rem;
            border: 1px solid var(--border-color);
            background-color: var(--container-bg);
            color: var(--text-color);
            box-shadow: 0 1px 3px 0 var(--shadow-color);
            font-size: 1rem;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .prompt-textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.25);
        }
        
        .generate-button {
            padding: 1rem 2rem;
            background-color: var(--accent-color);
            color: #FFFFFF;
            border: none;
            border-radius: 0.75rem;
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 6px -1px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .generate-button:hover {
            opacity: 0.9;
        }
        
        .generate-button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }

        .code-preview-panels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            height: 600px;
        }

        .code-panel, .preview-panel {
            background-color: var(--container-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 var(--shadow-color);
            display: flex;
            flex-direction: column;
            position: relative;
            transition: all 0.3s ease;
        }

        .code-header, .preview-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .code-editor {
            flex-grow: 1;
            width: 100%;
            height: 100%;
            background-color: transparent;
            color: var(--text-color);
            border: none;
            padding: 1rem;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.875rem;
            resize: none;
            white-space: pre;
            overflow-x: auto;
        }

        .code-editor:focus {
            outline: none;
        }
        
        .copy-button {
            background-color: var(--accent-color);
            color: #fff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .copy-button:hover {
            opacity: 0.9;
        }
        
        .preview-iframe {
            flex-grow: 1;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 0 0 0.75rem 0.75rem;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            border-radius: 0.75rem;
            z-index: 10;
        }

        @media (max-width: 768px) {
            .app-container {
                padding: 1rem;
            }
            
            header {
                flex-direction: column;
                gap: 1rem;
            }

            .code-preview-panels {
                grid-template-columns: 1fr;
                height: auto;
            }
        }
    </style>
</head>
<body class="light">

    <header>
        <div class="header-title">JJ Code AI Builder</div>
        <button id="theme-switcher" class="theme-switcher" aria-label="Toggle dark mode">
            <!-- Sun icon -->
            <svg id="sun-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6" fill="currentColor">
                <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.75 8.25a.75.75 0 010 1.5h-2.25a.75.75 0 010-1.5h2.25zM12 18.75a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0v-2.25a.75.75 0 01.75-.75zM17.25 12a5.25 5.25 0 00-10.5 0h10.5zM4.5 9a.75.75 0 010 1.5H2.25a.75.75 0 010-1.5H4.5zM17.25 15.75a.75.75 0 01-1.06 1.06l-1.5-1.5a.75.75 0 011.06-1.06l1.5 1.5zM7.5 7.5a.75.75 0 011.06-1.06l1.5 1.5a.75.75 0 01-1.06 1.06l-1.5-1.5z"/>
            </svg>
            <!-- Moon icon -->
            <svg id="moon-icon" class="hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6" fill="currentColor">
                <path d="M9.544 19.824a7.5 7.5 0 01-2.903-12.793A8.47 8.47 0 0012 3.75a8.47 8.47 0 005.359 1.931 7.5 7.5 0 01-5.815 14.143z"/>
            </svg>
        </button>
    </header>

    <main class="app-container">
        <section class="hero-section">
            <h2>JJ Code AI Builder: Turn Your Ideas into Code, Instantly.</h2>
            <p>Describe your web component in plain language, and let our advanced AI write the HTML, CSS, and JavaScript for you. The future of web development is here.</p>
        </section>

        <section class="main-content">
            <div class="prompt-input-group">
                <textarea id="prompt-input" class="prompt-textarea" placeholder="Describe the web component you want to build..."></textarea>
                <button id="generate-button" class="generate-button">Generate Code</button>
            </div>

            <div class="code-preview-panels">
                <div class="code-panel">
                    <div class="code-header">
                        <span>Generated Code</span>
                        <button id="copy-button" class="copy-button">Copy</button>
                    </div>
                    <textarea id="code-editor" class="code-editor" readonly></textarea>
                    <div id="loading-overlay" class="loading-overlay hidden">
                        Generating code...
                    </div>
                </div>

                <div class="preview-panel">
                    <div class="preview-header">
                        <span>Live Preview</span>
                    </div>
                    <iframe id="live-preview" class="preview-iframe"></iframe>
                </div>
            </div>
        </section>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- DOM Elements ---
            const body = document.body;
            const themeSwitcher = document.getElementById('theme-switcher');
            const sunIcon = document.getElementById('sun-icon');
            const moonIcon = document.getElementById('moon-icon');
            const promptInput = document.getElementById('prompt-input');
            const generateButton = document.getElementById('generate-button');
            const codeEditor = document.getElementById('code-editor');
            const copyButton = document.getElementById('copy-button');
            const livePreview = document.getElementById('live-preview');
            const loadingOverlay = document.getElementById('loading-overlay');

            // --- Theme Toggling ---
            themeSwitcher.addEventListener('click', () => {
                body.classList.toggle('dark');
                const isDark = body.classList.contains('dark');
                if (isDark) {
                    sunIcon.classList.add('hidden');
                    moonIcon.classList.remove('hidden');
                } else {
                    sunIcon.classList.remove('hidden');
                    moonIcon.classList.add('hidden');
                }
            });

            // --- API Call and Code Generation ---
            generateButton.addEventListener('click', async () => {
                const userPrompt = promptInput.value;
                if (!userPrompt) {
                    alert('Please enter a description for the code you want to generate.');
                    return;
                }

                // Show loading state
                loadingOverlay.classList.remove('hidden');
                generateButton.disabled = true;

                // Clear previous content
                codeEditor.value = '';
                livePreview.srcdoc = '';

                try {
                    const promptForAI = `Generate a single, self-contained HTML file with embedded CSS and JavaScript for a web component based on this description: ${userPrompt}. Ensure the output is a single markdown code block with the 'html' language specifier.`;
                    
                    const chatHistory = [{ role: "user", parts: [{ text: promptForAI }] }];
                    const payload = { contents: chatHistory };
                    // The API key should be an empty string for Canvas to provide it at runtime.
                    const apiKey = "AIzaSyAUekGjjW_ax7TthAo3WK_YXHuOevZfBnI"; 
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-preview-05-20:generateContent?key=${apiKey}`;

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`API error: ${response.status} ${response.statusText}`);
                    }

                    const result = await response.json();
                    
                    if (result.candidates && result.candidates.length > 0 && 
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        
                        const aiResponseText = result.candidates[0].content.parts[0].text;
                        
                        // Extract the code block from the markdown
                        const codeMatch = aiResponseText.match(/```html\n([\s\S]*)\n```/);
                        const generatedCode = codeMatch ? codeMatch[1] : aiResponseText;

                        // Start typewriter effect
                        typeWriterEffect(generatedCode, codeEditor, () => {
                            // After typewriter effect, update live preview
                            livePreview.srcdoc = generatedCode;
                        });

                    } else {
                        throw new Error('No valid response from AI.');
                    }
                } catch (error) {
                    console.error('Error generating code:', error);
                    alert('Failed to generate code. Please try again.');
                } finally {
                    // Hide loading state
                    loadingOverlay.classList.add('hidden');
                    generateButton.disabled = false;
                }
            });

            // --- Typewriter Effect ---
            function typeWriterEffect(text, element, callback) {
                let i = 0;
                element.value = '';
                const speed = 10; // Typing speed in milliseconds
                
                function type() {
                    if (i < text.length) {
                        element.value += text.charAt(i);
                        i++;
                        setTimeout(type, speed);
                    } else if (callback) {
                        callback();
                    }
                }
                type();
            }

            // --- Copy to Clipboard ---
            copyButton.addEventListener('click', () => {
                codeEditor.select();
                codeEditor.setSelectionRange(0, 99999); // For mobile devices
                try {
                    document.execCommand('copy');
                    copyButton.textContent = 'Copied!';
                    setTimeout(() => {
                        copyButton.textContent = 'Copy';
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy text:', err);
                    alert('Copying to clipboard failed.');
                }
            });

            // --- Initial Setup ---
            loadingOverlay.classList.add('hidden'); // Hide on load
        });
    </script>
</body>
</html>
